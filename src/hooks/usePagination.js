import { useState, useMemo, useEffect, useRef } from 'react';

const usePagination = (data, itemsPerPage = 6, enableInfiniteScroll = true) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [displayedItemsCount, setDisplayedItemsCount] = useState(itemsPerPage);
  const observerRef = useRef(null);
  const loadingRef = useRef(null);

  // Calculate total pages
  const totalPages = Math.ceil(data.length / itemsPerPage);

  // Get current page data (for traditional pagination)
  const currentData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  }, [data, currentPage, itemsPerPage]);

  // Get paginated data up to current page (for infinite scroll)
  const paginatedData = useMemo(() => {
    if (enableInfiniteScroll) {
      const result = data.slice(0, displayedItemsCount);
      console.log('Infinite scroll - displaying:', result.length, 'of', data.length, 'items');
      return result;
    } else {
      const endIndex = currentPage * itemsPerPage;
      return data.slice(0, endIndex);
    }
  }, [data, currentPage, itemsPerPage, displayedItemsCount, enableInfiniteScroll]);

  // Infinite scroll observer
  useEffect(() => {
    if (!enableInfiniteScroll || !loadingRef.current) {
      console.log('Observer not set up:', { enableInfiniteScroll, hasLoadingRef: !!loadingRef.current });
      return;
    }

    console.log('Setting up intersection observer');
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        console.log('Intersection observed:', { isIntersecting: target.isIntersecting, displayedItemsCount, totalItems: data.length });
        if (target.isIntersecting && displayedItemsCount < data.length) {
          console.log('Loading more items...');
          setDisplayedItemsCount(prev => {
            const newCount = Math.min(prev + itemsPerPage, data.length);
            console.log('New displayed count:', newCount);
            return newCount;
          });
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px'
      }
    );

    observer.observe(loadingRef.current);
    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enableInfiniteScroll, displayedItemsCount, data.length, itemsPerPage]);

  // Navigation functions
  const goToPage = (page) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const loadMore = () => {
    if (enableInfiniteScroll) {
      setDisplayedItemsCount(prev => Math.min(prev + itemsPerPage, data.length));
    } else if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Reset pagination when data changes
  const resetPagination = () => {
    setCurrentPage(1);
    setDisplayedItemsCount(itemsPerPage);
  };

  // Reset displayed items when data changes
  useEffect(() => {
    if (enableInfiniteScroll) {
      setDisplayedItemsCount(itemsPerPage);
    }
    setCurrentPage(1);
  }, [data.length, itemsPerPage, enableInfiniteScroll]);

  const hasMoreItems = enableInfiniteScroll
    ? displayedItemsCount < data.length
    : currentPage < totalPages;

  return {
    currentData,
    paginatedData,
    currentPage,
    totalPages,
    itemsPerPage,
    hasNextPage: hasMoreItems,
    hasPrevPage: currentPage > 1,
    goToPage,
    nextPage,
    prevPage,
    loadMore,
    resetPagination,
    totalItems: data.length,
    displayedItemsCount,
    startIndex: enableInfiniteScroll ? 1 : (currentPage - 1) * itemsPerPage + 1,
    endIndex: enableInfiniteScroll ? displayedItemsCount : Math.min(currentPage * itemsPerPage, data.length),
    loadingRef,
    hasMoreItems,
    enableInfiniteScroll
  };
};

export default usePagination;
