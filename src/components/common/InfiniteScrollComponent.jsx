import { 
  Box, 
  Typography, 
  Chip,
  CircularProgress,
  Fade
} from '@mui/material';
import { 
  ExpandMore 
} from '@mui/icons-material';

const InfiniteScrollComponent = ({
  totalItems,
  displayedItemsCount,
  hasMoreItems,
  loadingRef,
  isLoading = false,
  color = '#667eea',
  showItemsInfo = true
}) => {
  if (totalItems <= 0) return null;

  return (
    <Box sx={{ mt: 4, mb: 2 }}>
      {/* Items info */}
      {showItemsInfo && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
          <Chip
            label={`Showing ${displayedItemsCount} of ${totalItems} items`}
            variant="outlined"
            sx={{
              borderColor: color,
              color: color,
              fontWeight: 'bold'
            }}
          />
        </Box>
      )}

      {/* Loading indicator and intersection observer target */}
      {hasMoreItems && (
        <Box
          ref={loadingRef}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            py: 3,
            minHeight: 60,
            width: '100%'
          }}
        >
          {isLoading ? (
            <Fade in={true}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CircularProgress
                  size={24}
                  sx={{
                    color: color
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{
                    color: color,
                    fontWeight: 'medium'
                  }}
                >
                  Loading more items...
                </Typography>
              </Box>
            </Fade>
          ) : (
            <Box sx={{ height: 20 }} /> // Invisible trigger area
          )}
        </Box>
      )}

      {/* End of list indicator */}
      {!hasMoreItems && displayedItemsCount > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
          <Chip
            label="You've reached the end!"
            sx={{
              bgcolor: `${color}15`,
              color: color,
              fontWeight: 'bold',
              border: `1px solid ${color}30`
            }}
          />
        </Box>
      )}


    </Box>
  );
};

export default InfiniteScrollComponent;
