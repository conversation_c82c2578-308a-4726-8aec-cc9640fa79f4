import { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  TextField,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
  IconButton,
  Collapse,
  Alert
} from '@mui/material';
import {
  Comment,
  Send,
  ExpandMore,
  ExpandLess,
  Person,
  Telegram
} from '@mui/icons-material';
import { collection, addDoc, query, where, orderBy, onSnapshot, serverTimestamp } from 'firebase/firestore';
import { db } from '../../firebaseConfig';
import { isTelegramWebApp, getTelegramUser, showTelegramAlert, isTestMode, getTestUser } from '../../utils/telegramUtils';

const CommentsSection = ({ itemId, itemType, color = '#667eea' }) => {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [showComments, setShowComments] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showAddComment, setShowAddComment] = useState(false);

  // Check if user can add comments (in Telegram or test mode)
  const canAddComments = () => {
    return isTelegramWebApp() || isTestMode();
  };

  // Get current user data
  const getCurrentUser = () => {
    if (isTelegramWebApp()) {
      return getTelegramUser();
    } else if (isTestMode()) {
      return getTestUser();
    }
    return null;
  };

  // Load comments
  useEffect(() => {
    const commentsRef = collection(db, 'comments');
    const q = query(
      commentsRef,
      where('itemId', '==', itemId),
      where('itemType', '==', itemType),
      where('status', '==', 'approved'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const commentsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setComments(commentsData);
    });

    return () => unsubscribe();
  }, [itemId, itemType]);

  // Add comment
  const handleAddComment = async () => {
    if (!newComment.trim()) return;

    const currentUser = getCurrentUser();
    if (!currentUser) {
      showTelegramAlert('You must be using the Telegram app to add comments');
      return;
    }

    setLoading(true);
    try {
      await addDoc(collection(db, 'comments'), {
        itemId,
        itemType,
        text: newComment.trim(),
        userId: currentUser.id,
        userFirstName: currentUser.firstName,
        userLastName: currentUser.lastName,
        username: currentUser.username,
        userPhotoUrl: currentUser.photoUrl,
        createdAt: serverTimestamp(),
        status: 'approved' // Auto-approve for now, can add moderation later
      });
      setNewComment('');
      setShowAddComment(false);
      showTelegramAlert('Comment added successfully!');
    } catch (error) {
      console.error('Error adding comment:', error);
      showTelegramAlert('Failed to add comment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getUserDisplayName = (comment) => {
    if (comment.userFirstName || comment.userLastName) {
      return `${comment.userFirstName || ''} ${comment.userLastName || ''}`.trim();
    }
    return comment.username || 'Anonymous User';
  };

  return (
    <Box sx={{ mt: 3 }}>
      {/* Comments Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Comment sx={{ color }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#2c3e50' }}>
            Comments ({comments.length})
          </Typography>
        </Box>
        
        <Button
          variant="outlined"
          size="small"
          onClick={() => setShowComments(!showComments)}
          endIcon={showComments ? <ExpandLess /> : <ExpandMore />}
          sx={{
            borderColor: color,
            color: color,
            '&:hover': {
              borderColor: color,
              backgroundColor: `${color}10`
            }
          }}
        >
          {showComments ? 'Hide' : 'Show'} Comments
        </Button>
      </Box>

      <Collapse in={showComments}>
        {/* Add Comment Section */}
        <Card sx={{ mb: 2, border: `1px solid ${color}30` }}>
          <CardContent sx={{ pb: 2 }}>
            {!showAddComment ? (
              <Button
                variant="contained"
                startIcon={<Comment />}
                onClick={() => {
                  if (canAddComments()) {
                    setShowAddComment(true);
                  } else {
                    showTelegramAlert('You must be using the Telegram app to add comments');
                  }
                }}
                sx={{
                  background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`,
                  '&:hover': {
                    background: `linear-gradient(135deg, ${color}dd 0%, ${color}bb 100%)`,
                  }
                }}
              >
                Add Comment
              </Button>
            ) : (
              <Box>
                {!canAddComments() && (
                  <Alert
                    severity="info"
                    icon={<Telegram />}
                    sx={{ mb: 2 }}
                  >
                    You must be using the Telegram app to add comments
                  </Alert>
                )}

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Write your comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  disabled={!canAddComments() || loading}
                  sx={{ mb: 2 }}
                />
                
                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setShowAddComment(false);
                      setNewComment('');
                    }}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<Send />}
                    onClick={handleAddComment}
                    disabled={!newComment.trim() || !canAddComments() || loading}
                    sx={{
                      background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`,
                      '&:hover': {
                        background: `linear-gradient(135deg, ${color}dd 0%, ${color}bb 100%)`,
                      }
                    }}
                  >
                    {loading ? 'Posting...' : 'Post Comment'}
                  </Button>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Comments List */}
        {comments.length > 0 ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {comments.map((comment) => (
              <Card key={comment.id} sx={{ border: '1px solid #e0e0e0' }}>
                <CardContent sx={{ pb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                    <Avatar
                      src={comment.userPhotoUrl}
                      sx={{ 
                        width: 40, 
                        height: 40,
                        bgcolor: color
                      }}
                    >
                      {!comment.userPhotoUrl && <Person />}
                    </Avatar>
                    
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          {getUserDisplayName(comment)}
                        </Typography>
                        {comment.username && (
                          <Chip
                            label={`@${comment.username}`}
                            size="small"
                            sx={{
                              bgcolor: `${color}15`,
                              color: color,
                              fontSize: '0.7rem'
                            }}
                          />
                        )}
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(comment.createdAt)}
                        </Typography>
                      </Box>
                      
                      <Typography variant="body2" sx={{ lineHeight: 1.5 }}>
                        {comment.text}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Comment sx={{ fontSize: 48, color: '#ccc', mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              No comments yet. Be the first to comment!
            </Typography>
          </Box>
        )}
      </Collapse>
    </Box>
  );
};

export default CommentsSection;
